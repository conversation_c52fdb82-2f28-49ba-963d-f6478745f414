terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 5.0.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = ">= 5.0.0"
    }
    random = {
      source = "hashicorp/random"
    }
    postgresql = {
      source  = "cyrilgdn/postgresql"
      version = "1.14.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 5"
    }
    kubernetes = {
      source = "hashicorp/kubernetes"
    }
    sentry = {
      source = "jianyuan/sentry"
    }
    pagerduty = {
      source  = "PagerDuty/pagerduty"
      version = "3.15.0"
    }
  }

  required_version = ">= 1.4.0, <1.5.0"

  backend "gcs" {
    bucket = "inspectorio-terraform-state"
    prefix = "terraform2.0/apps/stg/infra"
  }
}

provider "google" {
  project = var.gcp_project_id
  region  = var.gcp_provider_region
  zone    = var.gcp_provider_zone
}

provider "google-beta" {
  project = var.gcp_project_id
  region  = var.gcp_provider_region
  zone    = var.gcp_provider_zone
}

provider "google" {
  alias   = "management-cluster"
  project = var.management_cluster_project_id
  region  = var.management_cluster_default_region
  zone    = "${var.management_cluster_default_region}-c"
}

data "google_client_config" "provider" {}

data "google_container_cluster" "gke_cluster" {
  count    = var.gke_endpoint_token == "" ? 1 : 0
  name     = var.gke_cluster_name
  location = var.gcp_provider_region
  project  = var.gcp_project_id
}

provider "kubernetes" {
  host                   = var.gke_endpoint_override == "" ? "https://${data.google_container_cluster.gke_cluster[0].endpoint}" : var.gke_endpoint_override
  token                  = var.gke_endpoint_token == "" ? data.google_client_config.provider.access_token : var.gke_endpoint_token
  cluster_ca_certificate = var.gke_cluster_ca_certificate == "" ? base64decode(data.google_container_cluster.gke_cluster[0].master_auth[0].cluster_ca_certificate) : var.gke_cluster_ca_certificate
}

# Cloudflare Provider setting
data "google_secret_manager_secret_version" "cloudflare-email" {
  provider = google.management-cluster
  secret   = "cloudflare-email"
}

data "google_secret_manager_secret_version" "cloudflare-api-key" {
  provider = google.management-cluster
  secret   = "cloudflare-api-key"
}

provider "cloudflare" {
  api_token = data.google_secret_manager_secret_version.cloudflare-api-key.secret_data
}

# Sentry provider setting
data "google_secret_manager_secret_version" "sentry-api-key" {
  provider = google.management-cluster
  secret   = "sentry-api-key"
}

provider "sentry" {
  token = data.google_secret_manager_secret_version.sentry-api-key.secret_data
}

# PagerDuty Provider setting
data "google_secret_manager_secret_version" "pagerduty-api-token" {
  provider = google.management-cluster
  secret   = "pagerduty-api-token"
}

provider "pagerduty" {
  token = data.google_secret_manager_secret_version.pagerduty-api-token.secret_data
}
