provider "postgresql" {
  scheme = "gcppostgres"
  alias  = "langtrace-main-pg16" ## please put in provider alias instance_name manually
  host   = "${var.gcp_project_id}:${var.gcp_provider_region}:langtrace-main-pg16"
  #host             = localhost
  port             = 5432
  username         = "postgres"
  password         = random_string.cloudsql_passwords["langtrace-main-pg16"].result
  expected_version = "6"
  sslmode          = "disable"
  max_connections  = 10
  superuser        = false
}

module "langtrace-main-pg16" { ## please put in module name instance_name manually
  source   = "../../../../modules/postgresql/instance"
  instance = local.cloudsql_instances["langtrace-main-pg16"]
  env      = var.env

  providers = {
    postgresql = postgresql.langtrace-main-pg16 ## please put in provider alias instance_name manually
  }

  depends_on = [
    random_integer.cloudsql_ports,
    module.projects
  ]
}

locals {
  app-langtrace = {
    iam = {
      custom_roles = {}
      roles        = {}
      sa = {
        roles = [
        ]
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.gcp_project_id}.svc.id.goog[langtrace/langtrace]"
          ]
        }
      }
    }
    secrets = {
    }
    domains = {
      "${var.env_main_zone}" = [ #stg.inspectorio.com
        {
          name    = "langtrace"
          ttl     = var.default_ttl
          type    = "CNAME"
          value   = var.gateway_service
          proxied = false
        }
      ]
    }
    cloudsql = {
      main-pg16 = {
        postgresql_version = "POSTGRES_16"
        tier               = "db-g1-small"
        user_labels = {
          profile_load = "oltp"
        }
        database_flags = [
          {
            name  = "cloudsql.logical_decoding"
            value = "on"
          }
        ]
        owner_users = []
        cdc         = false
        databases = {
          langtrace = {
            schemas = {
              public = {
                rw_users = []
                ro_users = []
              }
            }
            additional_extensions = []
          }
        }
      }
    }
  }
}
