locals {
  apps = {
    certificates                  = local.app-certificates
    clickhouse-operator           = local.app-clickhouse-operator
    cloudflare-tunnel             = local.app-cloudflare-tunnel
    datadog                       = local.app-datadog
    devops-exporter               = local.app-devops-exporter
    devops-portal-be              = local.app-devops-portal-be
    devpi-proxy                   = local.app-devpi-proxy
    eck-datasync                  = local.app-eck-datasync
    eck-infra                     = local.app-eck-infra
    eck-integration               = local.app-eck-integration
    elastic-ui                    = local.app-elastic-ui
    external-secrets              = local.app-external-secrets
    fluentd                       = local.app-fluentd
    gcp-notify                    = local.app-gcp-notify
    # gitlab-dr                     = local.app-gitlab-dr # This one is for gitlab DR practices
    gitlab-runner                 = local.app-gitlab-runner
    infrabot                      = local.app-infrabot
    keda                          = local.app-keda
    kong-gateway                  = local.app-kong-gateway
    monitoring                    = local.app-monitoring
    mrnotifybot                   = local.app-mrnotifybot
    opencost                      = local.app-opencost
    pagerduty-integration-webhook = local.app-pagerduty-integration-webhook
    alerts-integration-webhook    = local.app-alerts-integration-webhook
    pgbouncer                     = local.app-pgbouncer
    poc-pgupgrade                 = local.app-poc-pgupgrade
    pomerium                      = local.app-pomerium
    postgresql                    = local.app-postgresql
    sample-service                = local.app-sample-service
    sftpgo                        = local.app-sftpgo
  }
}
