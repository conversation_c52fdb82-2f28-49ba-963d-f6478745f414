# provider "postgresql" {
#   scheme = "gcppostgres"
#   alias  = "gitlab-dr-main" ## please put in provider alias instance_name manually
#   host   = "${var.gcp_project_id}:${var.gcp_provider_region}:gitlab-dr-main"
#   #host             = localhost
#   port             = 5432
#   username         = "postgres"
#   password         = random_string.cloudsql_passwords["gitlab-dr-main"].result
#   expected_version = "14"
#   sslmode          = "disable"
#   max_connections  = 10
#   superuser        = false
# }

# module "gitlab-dr-main" { ## please put in module name instance_name manually
#   source   = "../../../../modules/postgresql/instance"
#   instance = local.cloudsql_instances["gitlab-dr-main"]
#   env      = var.env

#   providers = {
#     postgresql = postgresql.gitlab-dr-main ## please put in provider alias instance_name manually
#   }

#   depends_on = [
#     random_integer.cloudsql_ports,
#     module.projects
#   ]
# }

# locals {
#   app-gitlab-dr = {
#     iam = {
#       custom_roles = {}
#       roles        = {}
#       sa = {
#         roles = [
#         ]
#         custom_roles = [
#           "bucketList"
#         ]
#         sa_iam_bindings = {
#           "roles/iam.workloadIdentityUser" = [
#             "serviceAccount:${var.gcp_project_id}.svc.id.goog[gitlab-dr/gitlab]"
#           ]
#         }
#       }
#     }
#     secrets = {}
#     cloudsql = {
#       main = {
#         postgresql_version = "POSTGRES_14"
#         tier               = "db-custom-4-8192"
#         availability_type  = "REGIONAL"
#         user_labels = {
#           profile_load = "oltp"
#           snapshots_4h = "true"
#         }
#         database_flags = [
#           {
#             name  = "max_connections"
#             value = "500"
#           },
#           {
#             name  = "work_mem"
#             value = "8192"
#           }
#         ]
#         owner_users = []
#         cdc         = false
#         databases = {
#           gitlab = {
#             schemas = {
#               public = {
#                 rw_users = []
#                 ro_users = []
#               }
#               gitlab_partitions_dynamic = {
#                 rw_users = []
#                 ro_users = []
#               }
#               gitlab_partitions_static = {
#                 rw_users = []
#                 ro_users = []
#               }
#             }
#             additional_extensions = ["pg_trgm", "btree_gist"]
#           }
#         }
#       }
#     }
#   }
# }
