locals {
  app-clickhouse-ds = {
    iam = {
      custom_roles = {}
      roles        = {}
      sa = {
        roles = [
          "roles/secretmanager.viewer"
        ]
        custom_roles = []
        sa_iam_bindings = {
          "roles/iam.workloadIdentityUser" = [
            "serviceAccount:${var.gcp_project_id}.svc.id.goog[clickhouse-ds/clickhouse-ds]"
          ]
        }
      }
    }
    clickhouse = {
      users = {
        default  = {}
        chowner  = {}
        chbackup = {}
        grafana  = {}
        analytic3 = {
          accessor = ["serviceAccount:<EMAIL>"]
        }
        superset = {
          accessor = ["serviceAccount:<EMAIL>",
            "serviceAccount:<EMAIL>"
          ]
        }
        superset_target = {
          accessor = ["serviceAccount:<EMAIL>",
          "serviceAccount:<EMAIL>"]
        }
        superset_walmart = {
          accessor = ["serviceAccount:<EMAIL>",
          "serviceAccount:<EMAIL>"]
        }
        airflow = {
          accessor = ["serviceAccount:<EMAIL>"]
        }
        developer = {
          accessor = local.engineering_teams.data_analytics_dev_users
        }
        connector = {
          accessor = ["serviceAccount:<EMAIL>"]
        }
        langtrace = {
          accessor = ["serviceAccount:<EMAIL>"]
        }
      }
    }
    gcs = {
      ch-backups = {
        prefix             = ""
        overwrite_name     = "${var.gcp_project_id}-${var.env}-clickhouse-ds-backups"
        storage_class      = "STANDARD"
        location           = var.gcp_provider_region
        bucket_policy_only = true
        admins             = ["group:<EMAIL>"]
        viewers            = []
        creators           = []
        versioning         = false
        cors               = {}
        lifecycle_rules    = []
      }
    }
  }
}
