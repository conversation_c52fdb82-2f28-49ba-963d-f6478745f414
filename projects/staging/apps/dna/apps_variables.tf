locals {
  apps = {
    airflow             = local.app-airflow
    analytic3           = local.app-analytic3
    analytics-superset  = local.app-analytics-superset
    apache-superset     = local.app-apache-superset
    clickhouse-ds       = local.app-clickhouse-ds
    datalake            = local.app-datalake
    dedupe-api          = local.app-dedupe-api
    defect-recommend-be = local.app-defect-recommend-be
    document-validator  = local.app-document-validator
    factory-risk-be     = local.app-factory-risk-be
    kafka-connect-cdc   = local.app-kafka-connect-cdc
    langtrace           = local.app-langtrace
    ml-store            = local.app-ml-store
    product-risk-be     = local.app-product-risk-be
    pub-capa            = local.app-pub-capa
    qa-platform         = local.app-qa-platform
    timing-formula      = local.app-timing-formula
  }
}
